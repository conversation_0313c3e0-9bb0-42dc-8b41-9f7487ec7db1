
import * as allBrowserDelegates from '../BrowserDelegates/index'

import WindowFactory, { getActiveWindow, setActiveWindow } from '../WindowFactory';
import { screen } from 'electron';

export async function openWindow({ pack, data }: any) {
    let screenSize = screen.getPrimaryDisplay().size
    let activeWindow = getActiveWindow()!
    let windowFactory = new WindowFactory(screenSize, __dirname)
    if (activeWindow && !activeWindow.isDestroyed()) {
        activeWindow.hide()
    }
    let { winEventer, setDelegate } = windowFactory.open({
        pack, data,
        unique: false,
        delegate: { ...allBrowserDelegates }
    })
    winEventer.on("closed", () => {
        console.log('DEBUG_LOG:subWindow closed');
        if (activeWindow && !activeWindow.isDestroyed()) {
            activeWindow.show()
            setActiveWindow(activeWindow)
            setDelegate()
        }
    })
    winEventer.on("loaded", (info) => {
        console.log('DEBUG_LOG:subWindow loaded', info);

    })
    winEventer.on('ipc-message', (info = []) => {
        console.log('DEBUG_LOG:subWindow message', info);

    })
}
export function closeWindow() {
    let activeWindow = getActiveWindow()!
    if (!activeWindow.isDestroyed()) {
        activeWindow.close()
    }
}
export function moveWindowToLeft() {
    let activeWindow = getActiveWindow()!
    if (activeWindow && !activeWindow.isDestroyed()) {
        activeWindow.setPosition(0, 0)
    }
}
export function moveWindowToRight() {
    let activeWindow = getActiveWindow()!
    if (activeWindow && !activeWindow.isDestroyed()) {
        let screenSize = screen.getPrimaryDisplay().size
        activeWindow.setPosition(screenSize.width - activeWindow.getSize()[0], 0)
    }
}