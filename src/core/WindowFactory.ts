import MenuBuilder from "../menu";
import SystemInfo from "systeminformation";
import { MAIN_WINDOW_SIZE } from "../config/const";
import { PROXY, ASSETS_PATH, WINDOW_ADAPTER } from "./Configure";
import { register } from "./Eventer";
import { TEST, DEBUG } from "../env";
import { app, BrowserWindow, session, ipcMain, systemPreferences } from "electron";
import url from "url";
import path from "path";

// Extend BrowserWindow to allow custom property $$name$$
declare module 'electron' {
	interface BrowserWindow {
		$$name$$?: string;
	}
}
import fs from "fs-extra";
import bridge from "./MessageBridge";
import { EventEmitter } from "events";


import mime from "mime-types";

let activeWindow: Electron.CrossProcessExports.BrowserWindow | null = null;

export function getActiveWindow(): Electron.CrossProcessExports.BrowserWindow | null {
	return activeWindow;
}
export function setActiveWindow(window: Electron.CrossProcessExports.BrowserWindow | null) {
	activeWindow = window;
}

export default class WindowFactory {
	$screen_size: any;
	$dirname: string;
	$error_page: string;
	constructor(screenSize: any, dirname: string) {
		this.$screen_size = screenSize;
		this.$error_page = "404 Not Found";
		this.$dirname = dirname;
		this.__init();
	}

	__init() {
		register("proxy-pass", ({ request, callback }: any) => {
			let location = url.parse(request.url);
			location.pathname = location.pathname || "";
			location.host = location.host || "";
			if (location.pathname == "/") {
				location.pathname = "index.html";
			}
			if (/^\/__root__/.test(location.pathname)) {
				const parsed = location.pathname.match(/^\/__root__\/([^/]+?)\/(.+)/);
				if (parsed) {
					location.host = parsed[1];
					location.pathname = parsed[2];
				}
			}
			const file = path.join(ASSETS_PATH, location.host, location.pathname),
				exist = fs.existsSync(file),
				mimeType = mime.lookup(location.pathname!);
			if (exist) {
				try {
					let data = fs.readFileSync(file);
					callback({ mimeType, data });
				} catch (e) {
					callback({ mimeType, data: Buffer.from(this.$error_page) });
				}
			} else {
				callback({ mimeType, data: Buffer.from(this.$error_page) });
			}
		});
		register("proxy-pass-updater", ({ request, callback }: any) => {
			console.log("on proxy-pass-updater", request);
			let location = url.parse(request.url);
			location.host = location.host || "";
			if (location.pathname == "/" || !location.pathname) {
				location.pathname = "index.html";
			}
			const file = path.join(this.$dirname, location.host, location.pathname),
				exist = fs.existsSync(file),
				mimeType = mime.lookup(location.pathname);
			if (exist) {
				try {
					let data = fs.readFileSync(file);
					callback({ mimeType, data });
				} catch (e) {
					callback({ mimeType, data: Buffer.from(this.$error_page) });
				}
			} else {
				callback({ mimeType, data: Buffer.from(this.$error_page) });
			}
		});
	}

	open({
		pack,
		delegate = {},
		data = {},
		unique = false,
		needSystemInfo = false,
	}: any) {
		if (unique) {
			BrowserWindow.getAllWindows().forEach((win: any) => {
				if (win.$$name$$ == pack) {
					win.close();
				}
			});
		}
		const winEventer = new EventEmitter();
		let { width, height } = data.size || MAIN_WINDOW_SIZE
		console.log("🚀 ~ WindowFactory ~ width, height:", width, height)
		let screenSize = this.$screen_size
		console.log("🚀 ~ WindowFactory ~ screenSize:", screenSize)
		console.log("🚀 ~ WindowFactory ~ data.ratio:", data.ratio)
		let ratio = Math.min(screenSize.width / width, screenSize.height / height);
		console.log("🚀 ~ WindowFactory ~ ratio:", ratio)
		ratio *= (data.ratio || 1);
		console.log("🚀 ~ WindowFactory ~ ratio:", ratio)
		console.log("🚀 ~ WindowFactory ~ width * ratio:", width * ratio)
		console.log("🚀 ~ WindowFactory ~ height * ratio:", height * ratio)

		let _window = new BrowserWindow({
			title: "豆神王者Club",
			width: width * ratio | 0,
			height: height * ratio | 0,
			resizable: true,
			center: true,
			frame: !data.noFrame,
			transparent: !!data.transparent,
			autoHideMenuBar: true,
			// alwaysOnTop: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false,
				// devTools: false,
				// nodeIntegration: true,
				// enableRemoteModule: true
			},
		});

		let userAgent = _window.webContents.getUserAgent(),
			url;
		_window.webContents.setUserAgent(
			`${userAgent} KCPC v${app.getVersion()} ${pack}`
		);
		if (DEBUG && WINDOW_ADAPTER[pack]) {
			url = WINDOW_ADAPTER[pack];
			console.log("url", url);
		} else {
			url = `${PROXY}://${pack}${TEST || DEBUG ? "?env=test" : ""}`;
		}
		if (data.remoteUrl) {
			url = data.remoteUrl;
		}
		_window.loadURL(url);
		if (DEBUG || TEST) {
			_window.webContents.openDevTools();
		}

		console.log('DEBUG_LOG:app.getPath("userData")', app.getPath("userData"));
		// 可以查看所有的event事件
		// const events = _window.webContents.eventNames();
		// events.forEach(event => {
		// 	_window.webContents.on(event, () => {
		// 		console.log(`on event: ${event}`);
		// 	});
		// });
		_window.webContents.on("ipc-message", (event, message) => {
			winEventer.emit("ipc-message", message);
		});
		_window.webContents.on("did-finish-load", () => {
			// _window.webContents.closeDevTools();

			if (data.remoteUrl) {
				// _window.hide()
			}
			_window.webContents.send("configure", {
				__dirname: path.resolve(__dirname, ".."),
				__apppath: app.getAppPath(),
				version: app.getVersion(),
				data,
				TEST,
				DEBUG,
			});

			if (needSystemInfo) {
				SystemInfo.getStaticData((info) => {
					_window.webContents.send("systeminfo", {
						systeminfo: info,
					});
				});
				console.log("send system info");
			}
			winEventer.emit("loaded");
		});
		//声网权限添加
		_window.webContents.once("did-finish-load", async () => {
			ipcMain.handle("IPC_REQUEST_PERMISSION_HANDLER", async (event, arg) => {
				if (
					systemPreferences.getMediaAccessStatus(arg.type) === "not-determined"
				) {
					console.log("main process request handler:" + JSON.stringify(arg));
					return await systemPreferences.askForMediaAccess(arg.type);
				}
			});
		});
		_window.on("closed", () => {
			for (let key in delegate) {
				console.log("remove delegate for key");
				bridge.removeDelegate(key);
			}
			winEventer.emit("closed");
		});
		_window.$$name$$ = pack;
		bridge.delegate = delegate;
		const menuBuilder = new MenuBuilder(_window);
		menuBuilder.buildMenu();
		const sendMessage = (...args: [channel: string, ...args: any[]]) => {
			if (!_window.isDestroyed()) {
				_window.webContents.send(...args);
			}
		}
		process.nextTick(() => {
			winEventer.emit("start");
		});

		const setDelegate = () => {
			bridge.delegate = delegate;
		}
		setDelegate()

		activeWindow = _window;
		return { winEventer, window: _window, sendMessage, setDelegate };
	}
}
