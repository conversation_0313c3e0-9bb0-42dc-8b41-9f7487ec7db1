import { app } from "electron"
import path from 'path'
import { DEBUG } from '../env'
export const APP_PATH = app.getPath("userData")
export const PACKAGE_PATH = path.join(APP_PATH, "packages")
export const ASSETS_PATH = path.join(APP_PATH, "appassets")
export const INSTALLED_META_FILE = "lastest-installed-meta"
export const PACKAGE_META_FILE = "lastest-downloaded-meta"
export const PROXY = "myapp"
export const PROXY_UPDATER = "updater"
export const WINDOW_ADAPTER: any = DEBUG ? {
    "main-ui": "http://localhost:5173",
    "main-ui#/liveroom-end/public-wall": "http://localhost:5173#/liveroom-end/public-wall"
} : {}
