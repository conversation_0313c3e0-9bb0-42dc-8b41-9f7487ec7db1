var IN_RENDER: boolean, electron: any

try {
	IN_RENDER = require('is-electron-renderer')
	electron = require('electron')
} catch (error) {
	console.warn('Failed to load electron modules, running in non-electron environment.')
}

const CHANNEL = 'MESSAGE_RPC_BRIDGE'
class MessageBridge {
	$uuid: number
	$handlers: any
	$sender_pool: any
	$sender: any
	$receiver: any
	$delegate: any
	constructor() {
		this.$uuid = 0
		this.$handlers = {}
		this.$sender_pool = {}
		this.$delegate = {}
		try {
			if (IN_RENDER) {
				this.$sender = electron.ipcRenderer
				this.$receiver = electron.ipcRenderer
			} else {
				this.$receiver = electron.ipcMain
			}
			this.__bind()
		} catch (error) {}
	}

	__bind() {
		this.$receiver.on(CHANNEL, (event: any, message: any) => {
			if (!IN_RENDER) {
				let sender = event.sender.getOwnerBrowserWindow()
				if (sender && 'id' in message) {
					this.$sender_pool[message.id] = sender
				}
			}
			this.__received(message)
		})
	}

	__send(data: any) {
		let sender = this.$sender
		if (!sender && 'id' in data) {
			sender = this.$sender_pool[data.id]
		}
		if (typeof data == 'object' && !IN_RENDER) {
			try {
				data = JSON.parse(JSON.stringify(data))
			} catch (error) {
				console.error('failed process')
			}
		}
		if (sender) {
			sender.send(CHANNEL, data)
		} else {
			console.error('no sender for message', data)
		}
	}

	__received(message: any) {
		if ('error' in message || 'result' in message) {
			const id = message.id
			const handler = this.$handlers[id]
			if (handler) {
				try {
					handler(message.error, message.result)
				} catch (e) {
					console.error('call message handler error', e)
				}
				delete this.$handlers[id]
				delete this.$sender_pool[id]
			} else {
				console.error('no callback handler for message', id)
			}
		} else if ('method' in message) {
			let method = this.$delegate[message.method],
				id = message.id
			if (!method) {
				this.__send({
					id,
					error: `no method named ${message.method}`
				})
				return
			}
			let sender = this.$sender || this.$sender_pool[id]
			Promise.resolve(method(message.args, sender))
				.then((result = null) => {
					if (message.id) {
						this.__send({
							id,
							result
						})
					}
				})
				.catch((error) => {
					this.__send({
						id,
						error: error.message
					})
				})
		}
	}

	set delegate(delegate: any) {
		for (let key in delegate) {
			this.$delegate[key] = delegate[key]
		}
	}

	removeDelegate(key: string) {
		delete this.$delegate[key]
	}

	call({ method, args, sender }: { method: string; args?: any; sender?: any }): Promise<any> {
		if (!IN_RENDER && !sender) {
			console.error('call from main process must set sender')
			return Promise.reject(new Error('call from main process must set sender'))
		}
		return new Promise((resolve, reject) => {
			let id = (IN_RENDER ? 'R' : 'M') + ++this.$uuid
			let handler = (err: any, result: unknown) => {
				if (err) {
					reject(err)
				} else {
					resolve(result)
				}
			}
			this.$handlers[id] = handler
			if (!IN_RENDER) {
				this.$sender_pool[id] = sender
			}
			this.__send({
				id,
				method,
				args
			})
		})
	}
}

export default new MessageBridge()
