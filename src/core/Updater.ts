import { DEBUG, TEST } from '../env'
import { BrowserWindow } from 'electron'
import bridge from './MessageBridge'

// const remote = import("@electron/remote/main");  // 1>引入
import * as remote from '@electron/remote/main' // 2>引入
remote.initialize()

import { EventEmitter } from 'events'
import MenuBuilder from '../menu'
export default class Updater extends EventEmitter {
	private $window: any = null
	private $dirname: string
	constructor(dirname: string) {
		super()
		this.$dirname = dirname
	}

	get win() {
		return this.$window
	}

	start() {
		let updateWindow: any = new BrowserWindow({
			width: 600,
			height: 300,
			resizable: false,
			center: true,
			frame: false,
			autoHideMenuBar: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false
			}
		})
		updateWindow.on('closed', () => {
			console.log('remove delegate openMainWindow')
			bridge.removeDelegate('openMainWindow')
			updateWindow = null
		})
		let url = DEBUG ? 'http://localhost:9007' : `updater://updater-dist`
		updateWindow.loadURL(url)
		remote.enable(updateWindow.webContents)
		bridge.delegate = {
			openMainWindow: async ({ pack, data }: { pack: string; data: any }) => {
				this.emit('open-main-window', { pack, data })
			}
		}
		const menuBuilder = new MenuBuilder(updateWindow)
		menuBuilder.buildMenu()
		this.$window = updateWindow
		if (DEBUG || TEST) {
			updateWindow.webContents.openDevTools()
		}
	}

	close() {
		if (this.$window) {
			this.$window.close()
		}
	}
}
