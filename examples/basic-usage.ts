// ============================================================================
// Electron IPC 架构使用示例
// ============================================================================

import { electronClient } from '../src/renderer/src/utils/electronClient'
import { electronServer } from '../src/core/ElectronServer'

// ============================================================================
// 渲染进程示例 (在 Vue 组件或其他渲染进程代码中使用)
// ============================================================================

export class RendererExample {
	async basicApiCalls() {
		try {
			// 1. 获取系统信息
			const systemInfo = await electronClient.getSystemInfo()
			console.log('系统信息:', systemInfo)

			// 2. 获取应用版本
			const version = await electronClient.getAppVersion()
			console.log('应用版本:', version)

			// 3. 文件操作
			const filePath = await electronClient.selectFile({
				title: '选择配置文件',
				filters: [
					{ name: 'JSON Files', extensions: ['json'] },
					{ name: 'All Files', extensions: ['*'] }
				]
			})

			if (filePath) {
				const content = await electronClient.readFile(filePath)
				console.log('文件内容:', content)
			}

			// 4. 应用设置
			const settings = await electronClient.getSettings()
			console.log('当前设置:', settings)

			await electronClient.updateSettings({
				theme: 'dark',
				notifications: true
			})

			// 5. HTTP 请求
			const response = await electronClient.httpRequest({
				url: 'https://api.github.com/repos/electron/electron',
				method: 'GET'
			})
			console.log('GitHub API 响应:', response.data)
		} catch (error) {
			console.error('API 调用失败:', error)
		}
	}

	setupEventListeners() {
		// 1. 监听应用事件
		const unsubscribeAppReady = electronClient.on('app-ready', () => {
			console.log('应用已准备就绪')
		})

		// 2. 监听更新事件
		const unsubscribeUpdate = electronClient.onAppUpdate((updateInfo) => {
			console.log('发现新版本:', updateInfo)
			// 显示更新提示
		})

		// 3. 监听窗口事件
		const unsubscribeFocus = electronClient.onWindowFocus(() => {
			console.log('窗口获得焦点')
		})

		const unsubscribeBlur = electronClient.onWindowBlur(() => {
			console.log('窗口失去焦点')
		})

		// 4. 监听系统主题变化
		const unsubscribeTheme = electronClient.onThemeChange((theme) => {
			console.log('系统主题变更为:', theme)
			// 更新应用主题
		})

		// 5. 监听通知
		const unsubscribeNotification = electronClient.onNotification((notification) => {
			console.log('收到通知:', notification)
			// 显示通知UI
		})

		// 6. 一次性监听
		electronClient.once('app-update-downloaded', (updateInfo) => {
			console.log('更新已下载完成:', updateInfo)
			// 提示用户重启应用
		})

		// 返回清理函数
		return () => {
			unsubscribeAppReady()
			unsubscribeUpdate()
			unsubscribeFocus()
			unsubscribeBlur()
			unsubscribeTheme()
			unsubscribeNotification()
		}
	}

	sendEventsToMain() {
		// 1. 发送用户操作事件
		electronClient.emitUserAction('button-click', {
			buttonId: 'save-button',
			page: '/settings'
		})

		// 2. 发送页面加载事件
		electronClient.emitPageLoaded(window.location.href, document.title, performance.now())

		// 3. 发送错误事件
		electronClient.emitError('Something went wrong', new Error().stack, 'USER_ERROR')
	}

	// Vue 组件中的完整示例
	vueComponentExample() {
		return {
			setup() {
				const systemInfo = ref(null)
				const notifications = ref([])

				// 组件挂载时
				onMounted(async () => {
					// 加载数据
					systemInfo.value = await electronClient.getSystemInfo()

					// 设置事件监听
					const cleanup = this.setupEventListeners()

					// 组件卸载时清理
					onUnmounted(() => {
						cleanup()
						electronClient.cleanup()
					})
				})

				return {
					systemInfo,
					notifications,
					// 方法
					selectFile: () => electronClient.selectFile(),
					minimizeWindow: () => electronClient.minimizeWindow()
					// ...其他方法
				}
			}
		}
	}
}

// ============================================================================
// 主进程示例 (在主进程代码中使用)
// ============================================================================

export class MainProcessExample {
	setupCustomAPI() {
		// 注册自定义 API 方法
		electronServer.registerAPI({
			// 用户管理
			getUserProfile: async (userId: string) => {
				// 模拟数据库查询
				return {
					id: userId,
					name: 'John Doe',
					email: '<EMAIL>',
					avatar: 'https://example.com/avatar.jpg'
				}
			},

			// 数据处理
			processData: async (data: any[]) => {
				// 执行数据处理逻辑
				const processed = data.map((item) => ({
					...item,
					processed: true,
					timestamp: Date.now()
				}))

				return {
					success: true,
					count: processed.length,
					data: processed
				}
			},

			// 系统操作
			openExternalUrl: async (url: string) => {
				const { shell } = require('electron')
				await shell.openExternal(url)
				return { opened: true }
			},

			// 异步任务
			performLongTask: async (taskId: string) => {
				// 模拟长时间运行的任务
				for (let i = 0; i <= 100; i += 10) {
					// 发送进度更新
					electronServer.broadcast('progress-update', {
						id: taskId,
						progress: i,
						status: i === 100 ? 'completed' : 'running',
						message: `处理中... ${i}%`
					})

					// 模拟延迟
					await new Promise((resolve) => setTimeout(resolve, 100))
				}

				return { taskId, completed: true }
			}
		})
	}

	setupEventHandlers() {
		// 监听渲染进程事件
		electronServer.onRendererEvent('user-action', (data) => {
			console.log('用户操作:', data)

			// 根据操作类型执行不同逻辑
			switch (data.action) {
				case 'button-click':
					this.handleButtonClick(data.data)
					break
				case 'page-view':
					this.trackPageView(data.data)
					break
				default:
					console.log('未知操作:', data.action)
			}
		})

		electronServer.onRendererEvent('error-occurred', (errorData) => {
			console.error('渲染进程错误:', errorData)

			// 错误上报逻辑
			this.reportError(errorData)
		})

		electronServer.onRendererEvent('page-loaded', (pageData) => {
			console.log('页面加载:', pageData)

			// 性能监控
			if (pageData.loadTime > 3000) {
				console.warn('页面加载时间过长:', pageData.loadTime)
			}
		})
	}

	broadcastSystemEvents() {
		// 定期广播系统状态
		setInterval(() => {
			electronServer.broadcast('system-status', {
				memory: process.memoryUsage(),
				uptime: process.uptime(),
				timestamp: Date.now()
			})
		}, 30000) // 每30秒

		// 监听系统事件并广播
		process.on('uncaughtException', (error) => {
			electronServer.broadcast('system-error', {
				message: error.message,
				stack: error.stack,
				timestamp: Date.now()
			})
		})
	}

	private handleButtonClick(data: any) {
		console.log('处理按钮点击:', data)
		// 具体的按钮点击处理逻辑
	}

	private trackPageView(data: any) {
		console.log('页面访问统计:', data)
		// 页面访问统计逻辑
	}

	private reportError(errorData: any) {
		console.log('上报错误:', errorData)
		// 错误上报到服务器的逻辑
	}
}

// ============================================================================
// 使用示例
// ============================================================================

// 在渲染进程中
// const rendererExample = new RendererExample()
// rendererExample.basicApiCalls()
// const cleanup = rendererExample.setupEventListeners()

// 在主进程中
// const mainExample = new MainProcessExample()
// mainExample.setupCustomAPI()
// mainExample.setupEventHandlers()
// mainExample.broadcastSystemEvents()
